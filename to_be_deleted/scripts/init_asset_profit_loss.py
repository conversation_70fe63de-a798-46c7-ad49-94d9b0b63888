

from collections import defaultdict
import copy
import datetime
from decimal import Decimal
import json
from typing import Dict
from flask import current_app
from app.business.account_pl import PROFIT_LOSS_BUSINESS_TYPES, TRADE_BUSINESS_TYPES
from app.business.external_dbs import ExchangeLogDB, PerpetualHistoryDB, TradeHistoryDB
from app.common.constants import AccountBalanceType, BalanceBusiness, PrecisionEnum
from app.models.wallet import AssetPrice
from app.utils.amount import quantize_amount
from app.utils.chicken_ribs import DefaultDictWithArg
from app.utils.date_ import current_timestamp, date_to_datetime, timestamp_to_datetime, today


def _query_table_by_time_range(table, fields, where, start_ts, end_ts):
    flag = False
    last_id = 0
    all_records = []
    origin_where = where
    while not flag:
        if last_id:
            where_ = f'{origin_where} and id < {last_id}'
        else:
            where_ = origin_where
        records = table.select(
                    *fields,
                    where=where_,
                    limit=50000,
                    order_by='id desc'
                )
        if not records:
            break
        records = list(dict(zip(fields, record)) for record in records)
        min_ts = min(r['time'] for r in records)
        last_id = min(r['id'] for r in records)
        all_records.extend(records)
        if min_ts < start_ts:
            flag = True
    return [r for r in all_records if start_ts <= r['time'] <= end_ts]


def _query_balance_transfer_history(table, start_ts, end_ts):
    balance_business_vals = {item.value for item in BalanceBusiness}
    result = []
    _db = TradeHistoryDB
    fields = ['id', 'user_id', 'time', 'account', 'business', 'asset', 'change', 'detail']
    where = ''            
    records = _query_table_by_time_range(table, fields, where, start_ts, end_ts)
    records = [r for r in records if r['business'] in balance_business_vals]
    current_app.logger.warning(f"Running balance history {table}, records count {len(records)} {start_ts} {end_ts} {_db}")
    result.extend(records)
    return result


def init_table():
    ExchangeLogDB.init_user_slice_asset_profit_loss_snapshot_tables()

def sync_user_asset_slice_profit_loss_snapshot(timestamp, balance_map, history):
    """
    币种维度盈亏分析快照
    """
    if timestamp is None:
        timestamp = current_timestamp(to_int=True)
    timestamp = timestamp - timestamp % 86400
    date_str = timestamp_to_datetime(timestamp).strftime('%Y-%m-%d')
    table_name = "user_slice_asset_profit_loss_snapshot"
    if ExchangeLogDB.slice_table_synced(table_name, date_str):
        return

    last_ts = timestamp - 86400
    last_dt = timestamp_to_datetime(last_ts)
    last_date_str = timestamp_to_datetime(last_ts).strftime("%Y-%m-%d")

    report_dt = timestamp_to_datetime(timestamp)

    # 依赖数据检查
    report_dt_balance_not_exist = last_dt_balance_not_exist = False
    if report_dt.date() not in balance_map:
        current_app.logger.warning(f"check user account balance error {report_dt.date()}")
        report_dt_balance_not_exist = True
    
    if last_dt.date() not in balance_map:
        current_app.logger.warning(f"check user account balance error {last_dt.date()}")
        last_dt_balance_not_exist = True

    ExchangeLogDB.clean_slice_table(table_name,
                           date_str,
                           last_date_str,
                           ExchangeLogDB.USER_SLICE_ASSET_PROFIT_LOSS_SNAPSHOT_TABLE_COUNT)
    ExchangeLogDB.insert_log(table_name, date_str)

    columns = ('user_id', 'asset', 'balance_usd')
    _last_date_key = "last_date"
    _current_date_key = "current_date"
    user_account_data = defaultdict(Decimal)

    user_assets_map = defaultdict(set)
    for ts_ in (last_ts, timestamp):
        table = ExchangeLogDB.user_account_balance_table(ts_)
        _s = table.select(
            *columns,
            where=f'account_type = "{AccountBalanceType.SPOT.name}"'
        )
        last_balance_map = balance_map[last_dt.date()]
        report_balance_map = balance_map[report_dt.date()]
        for user_id, bal_map in last_balance_map.items():
            for asset, balance_usd in bal_map.items():
                user_account_data[(user_id, asset, _last_date_key)] = balance_usd
                user_assets_map[user_id].add(asset)
        
        for user_id, bal_map in report_balance_map.items():
            for asset, balance_usd in bal_map.items():
                user_account_data[(user_id, asset, _current_date_key)] = balance_usd
                user_assets_map[user_id].add(asset)
    tables: Dict[int, ExchangeLogDB.Table] \
        = DefaultDictWithArg(ExchangeLogDB.user_slice_asset_profit_loss_snapshot_table)
    user_id_to_idx = ExchangeLogDB.user_slice_asset_profit_loss_hash

    empty_data = {
        "balance_usd": Decimal(),
        "last_day_usd": Decimal(),
        "net_transfer_in_usd": Decimal(),
        "profit_usd": Decimal(),
        "profit_rate": Decimal(),
        "total_transfer_in_usd": Decimal()
    }
    # key: (user_id, asset)
    final_result = defaultdict(lambda: copy.copy(empty_data))
    
    all_user_ids = sorted({_key[0] for _key in user_account_data.keys()})

    all_types = PROFIT_LOSS_BUSINESS_TYPES + TRADE_BUSINESS_TYPES
    pl_balance_types = {item.value for item in all_types}
    history = [item for item in history if item['business'] in pl_balance_types]
    total_transfer_in_map = defaultdict(lambda: defaultdict(Decimal))
    net_transfer_in_map = defaultdict(lambda: defaultdict(Decimal))
    price_map = AssetPrice.get_close_price_map(last_dt)
    for item in history:
        amount = item['change']
        user_id = item['user_id']
        asset = item['asset']
        detail = item['detail']
        price = 0
        if detail:
            try:
                detail = json.loads(detail)
                price = Decimal(detail.get('p', price))
            except Exception:
                pass
        if not price:
            price = price_map.get(asset, Decimal())
        val = amount * price
        if amount > 0:
            total_transfer_in_map[user_id][asset] += val
            net_transfer_in_map[user_id][asset] += val
        else:
            net_transfer_in_map[user_id][item['asset']] -= abs(val)

    account_type_name = AccountBalanceType.SPOT.name # 目前只支持现货账户
    for user_id in all_user_ids:
        user_assets = user_assets_map[user_id]
        for asset in user_assets:
            k = final_result[(user_id, asset)]
            k["balance_usd"] = user_account_data[(user_id, asset, _current_date_key)]
            k["last_day_usd"] = user_account_data[(user_id, asset, _last_date_key)]
            k["total_transfer_in_usd"] = \
                quantize_amount(total_transfer_in_map[user_id][asset], PrecisionEnum.CASH_PLACES)
            k['net_transfer_in_usd'] = \
                quantize_amount(net_transfer_in_map[user_id][asset], PrecisionEnum.CASH_PLACES)
            k["profit_usd"] = k["balance_usd"] - k["last_day_usd"] - k["net_transfer_in_usd"]
            principal_usd = k["last_day_usd"] + k["total_transfer_in_usd"]
            principal_usd = quantize_amount(principal_usd, PrecisionEnum.CASH_PLACES)
            k["profit_rate"] = k["profit_usd"] / principal_usd \
                if principal_usd != Decimal() else Decimal()
            # special logic here
            if last_dt_balance_not_exist:
                k["profit_usd"] = 0
                k["profit_rate"] = 0
                current_app.logger.warning(f"last day {last_dt.date()} balance not exist, set profit to ")


    for _key, _user_data in final_result.items():
        user_id, asset = _key
        if _user_data == empty_data:
            # empty data.
            continue
        table = tables[user_id_to_idx(user_id)]
        table.insert(
            last_date_str, user_id, asset, account_type_name,
            _user_data["balance_usd"], 
            _user_data["last_day_usd"],
            _user_data["net_transfer_in_usd"],
            _user_data["total_transfer_in_usd"],
            _user_data["profit_usd"], 
            _user_data["profit_rate"],
        )
    for table in tables.values():
        table.flush()

    ExchangeLogDB.update_log_finished(table_name, date_str)

def flush_data(start_date, end_date):
    balance_map = get_users_balance(start_date - datetime.timedelta(days=1), end_date)
    balance_business_vals = {item.value for item in BalanceBusiness}
    _db = TradeHistoryDB
    balance_history_map = defaultdict(list)

    start_ts = int(date_to_datetime(start_date).timestamp())
    end_ts = int(date_to_datetime(end_date).timestamp())
    for i in range(_db.DB_COUNT):
        for j in range(_db.TABLE_COUNT):
            table_name = 'balance_history'
            db_, _table = _db.DBS[i], f'{table_name}_{j}'
            records = _query_balance_transfer_history(db_.table(_table), start_ts, end_ts)
            records = [r for r in records if r['business'] in balance_business_vals]
            for item in records:
                date_ = timestamp_to_datetime(item['time']).date()
                balance_history_map[date_].append(item)
            current_app.logger.warning(f"Running balance history {i} {j}, records count {len(records)} {start_date} {end_date} {_db}")
    while start_date <= end_date:
        history = balance_history_map.get(start_date, [])
        ts = int(date_to_datetime(start_date).timestamp())
        sync_user_asset_slice_profit_loss_snapshot(ts, balance_map, history)
        current_app.logger.warning(f"sync user asset slice profit loss done {start_date}")
        start_date += datetime.timedelta(days=1)


def get_users_balance(start_date, end_date):
    result = defaultdict(lambda: defaultdict(Decimal))
    for i in range(200):
        table = ExchangeLogDB.user_slice_balance_table(i)
        records = table.select(
            "user_id", "market_value", "report_date", "asset",
            where=f"report_date >= '{start_date}' and report_date <= '{end_date}' and account=0",
        )
        for user_id, balance, report_date, asset in records:
            result[report_date][user_id][asset] += balance
    return result



def main():
    init_table()
    end_date = today()
    start_date = end_date - datetime.timedelta(days=60)
    curr = start_date
    while curr < end_date:
        start = curr
        end = curr + datetime.timedelta(days=10)
        end = min(end, end_date)
        flush_data(start, end)
        curr = end

if __name__ == '__main__':
    from app import create_app
    with create_app().app_context():
        main()
